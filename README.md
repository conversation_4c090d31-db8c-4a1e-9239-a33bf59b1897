# 🌿 Plant Scanner App

An AI-powered mobile app built with Expo that helps you identify plants using your phone's camera. Simply take a photo of any plant and get detailed information including plant name, care instructions, and interesting facts.

## ✨ Features

- 📸 **Camera Integration**: Take photos directly from the app
- 🖼️ **Gallery Access**: Select existing photos from your device
- 🤖 **AI-Powered Analysis**: Uses Google's Gemini 2.5 Flash model via OpenRouter API
- 📋 **Detailed Plant Info**: Get comprehensive information about identified plants
- 💡 **Care Instructions**: Learn how to properly care for your plants
- 🎨 **Modern UI**: Clean, intuitive interface with smooth animations

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or later)
- Expo CLI
- OpenRouter API key

### Installation

1. **<PERSON>lone and navigate to the project**
   ```bash
   cd expo-mvp-plant-identifier-250725
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure API Key**
   - Get your API key from [OpenRouter](https://openrouter.ai/)
   - Open `config/api.ts`
   - Replace `YOUR_OPENROUTER_API_KEY_HERE` with your actual API key

4. **Start the development server**
   ```bash
   npx expo start
   ```

5. **Run on your device**
   - Scan the QR code with Expo Go app (iOS/Android)
   - Or press `i` for iOS simulator, `a` for Android emulator

## 📱 How to Use

1. **Take or Select Photo**: Use the camera button to take a new photo or the gallery button to select an existing image
2. **Analyze Plant**: Tap the "Analyze Plant" button to send the image to the AI
3. **View Results**: Get detailed information about the plant including:
   - Plant name (common and scientific)
   - Plant type and characteristics
   - Care instructions (light, water, soil requirements)
   - Interesting facts
   - Confidence level of identification

## 🛠️ Technical Details

- **Framework**: Expo (React Native)
- **AI Model**: Google Gemini 2.5 Flash
- **API**: OpenRouter
- **Camera**: expo-camera, expo-image-picker
- **UI**: Modern React Native components with custom styling

## 📁 Project Structure

```
expo-mvp-plant-identifier-250725/
├── app/
│   ├── (tabs)/
│   │   ├── index.tsx          # Main scanner screen
│   │   ├── explore.tsx        # About/info screen
│   │   └── _layout.tsx        # Tab navigation layout
│   └── _layout.tsx            # Root layout
├── components/
│   └── PlantScanner.tsx       # Main scanner component
├── config/
│   └── api.ts                 # API configuration
├── services/
│   └── plantAnalysis.ts       # OpenRouter API service
└── assets/                    # Images and fonts
```

## 🔧 Configuration

### API Configuration (`config/api.ts`)

```typescript
export const API_CONFIG = {
  OPENROUTER_API_KEY: 'your-api-key-here',
  OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1/chat/completions',
  MODEL: 'google/gemini-2.5-flash',
};
```

## 📸 Tips for Best Results

- Take clear, well-lit photos
- Include distinctive features like leaves, flowers, or bark
- Avoid blurry or heavily shadowed images
- Multiple angles can help with identification

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📄 License

This project is licensed under the MIT License.
