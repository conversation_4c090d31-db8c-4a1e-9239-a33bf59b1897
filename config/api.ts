// API Configuration
export const API_CONFIG = {
  OPENROUTER_API_KEY: '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', // Replace with your actual API key
  OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1/chat/completions',
  MODEL: 'google/gemini-2.5-flash',
};

export const PLANT_ANALYSIS_PROMPT = `You are a plant identification expert. Analyze the image provided and give detailed information about the plant. Please provide:

1. **Plant Name**: Common name and scientific name if identifiable
2. **Plant Type**: (e.g., flowering plant, tree, shrub, succulent, etc.)
3. **Key Characteristics**: Notable features visible in the image
4. **Care Instructions**: Basic care requirements (light, water, soil)
5. **Interesting Facts**: Any notable information about the plant
6. **Confidence Level**: How confident you are in the identification (High/Medium/Low)

If you cannot identify the plant with confidence, please explain what you can observe and suggest general plant care tips based on the visible characteristics.

Format your response in a clear, structured way that's easy to read.`;
