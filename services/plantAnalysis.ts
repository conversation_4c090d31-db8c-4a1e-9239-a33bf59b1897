import { API_CONFIG, PLANT_ANALYSIS_PROMPT } from '../config/api';

export interface PlantAnalysisResult {
  success: boolean;
  analysis?: string;
  error?: string;
}

export class PlantAnalysisService {
  static async analyzePlant(imageUri: string): Promise<PlantAnalysisResult> {
    try {
      // Check if API key is configured
      if (API_CONFIG.OPENROUTER_API_KEY === 'YOUR_OPENROUTER_API_KEY_HERE') {
        return {
          success: false,
          error: 'Please configure your OpenRouter API key in config/api.ts'
        };
      }

      // Convert image to base64 data URL
      const base64Image = await this.convertImageToBase64(imageUri);
      
      const requestBody = {
        model: API_CONFIG.MODEL,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: PLANT_ANALYSIS_PROMPT
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64Image
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      };

      const response = await fetch(API_CONFIG.OPENROUTER_BASE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://plant-scanner-app.com', // Optional: your app's URL
          'X-Title': 'Plant Scanner App' // Optional: your app's name
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      
      if (data.choices && data.choices.length > 0) {
        return {
          success: true,
          analysis: data.choices[0].message.content
        };
      } else {
        throw new Error('No analysis result received from API');
      }

    } catch (error) {
      console.error('Plant analysis error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private static async convertImageToBase64(imageUri: string): Promise<string> {
    try {
      // For Expo, we can use fetch to get the image as blob, then convert to base64
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw new Error(`Failed to convert image to base64: ${error}`);
    }
  }
}
