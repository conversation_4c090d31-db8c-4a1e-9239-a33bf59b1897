import { StyleSheet } from 'react-native';

import { Collapsible } from '@/components/Collapsible';
import { ExternalLink } from '@/components/ExternalLink';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function AboutScreen() {
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A8E6CF', dark: '#2D5A3D' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#4CAF50"
          name="leaf.fill"
          style={styles.headerImage}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">🌿 About Plant Scanner</ThemedText>
      </ThemedView>
      <ThemedText>
        Plant Scanner is an AI-powered app that helps you identify plants using your phone's camera.
      </ThemedText>

      <Collapsible title="How it works">
        <ThemedText>
          1. Take a photo or select an image from your gallery
        </ThemedText>
        <ThemedText>
          2. Tap "Analyze Plant" to send the image to our AI
        </ThemedText>
        <ThemedText>
          3. Get detailed information about the plant including:
        </ThemedText>
        <ThemedText style={styles.bulletPoint}>• Plant name (common and scientific)</ThemedText>
        <ThemedText style={styles.bulletPoint}>• Plant type and characteristics</ThemedText>
        <ThemedText style={styles.bulletPoint}>• Care instructions</ThemedText>
        <ThemedText style={styles.bulletPoint}>• Interesting facts</ThemedText>
      </Collapsible>

      <Collapsible title="AI Technology">
        <ThemedText>
          This app uses <ThemedText type="defaultSemiBold">Google's Gemini 2.5 Flash</ThemedText> model
          through OpenRouter API for accurate plant identification and analysis.
        </ThemedText>
        <ThemedText>
          The AI can identify thousands of plant species and provide detailed care information.
        </ThemedText>
      </Collapsible>

      <Collapsible title="Setup Instructions">
        <ThemedText>
          To use the plant analysis feature, you need to:
        </ThemedText>
        <ThemedText>
          1. Get an API key from <ThemedText type="defaultSemiBold">OpenRouter</ThemedText>
        </ThemedText>
        <ThemedText>
          2. Replace the placeholder in <ThemedText type="defaultSemiBold">config/api.ts</ThemedText>
        </ThemedText>
        <ExternalLink href="https://openrouter.ai/">
          <ThemedText type="link">Get OpenRouter API Key</ThemedText>
        </ExternalLink>
      </Collapsible>

      <Collapsible title="Features">
        <ThemedText style={styles.bulletPoint}>📸 Camera integration</ThemedText>
        <ThemedText style={styles.bulletPoint}>🖼️ Photo gallery access</ThemedText>
        <ThemedText style={styles.bulletPoint}>🤖 AI-powered plant identification</ThemedText>
        <ThemedText style={styles.bulletPoint}>📋 Detailed plant information</ThemedText>
        <ThemedText style={styles.bulletPoint}>💡 Care instructions</ThemedText>
        <ThemedText style={styles.bulletPoint}>🎨 Modern, clean UI</ThemedText>
      </Collapsible>

      <Collapsible title="Tips for best results">
        <ThemedText>
          • Take clear, well-lit photos
        </ThemedText>
        <ThemedText>
          • Include distinctive features like leaves, flowers, or bark
        </ThemedText>
        <ThemedText>
          • Avoid blurry or heavily shadowed images
        </ThemedText>
        <ThemedText>
          • Multiple angles can help with identification
        </ThemedText>
      </Collapsible>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#4CAF50',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bulletPoint: {
    marginLeft: 16,
    marginVertical: 2,
  },
});
