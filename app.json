{"expo": {"name": "expo-mvp-plant-identifier-250725", "slug": "expo-mvp-plant-identifier-250725", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "expomvpplantidentifier250725", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.geoattract.expomvpplantidentifier250725", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.geoattract.expomvpplantidentifier250725"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "7cc2d11b-9f0f-4d51-bc70-dc3008f51de4"}}, "owner": "geoattract"}}